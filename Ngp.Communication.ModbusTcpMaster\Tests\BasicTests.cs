using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Tests;

/// <summary>
/// Basic tests for the Modbus TCP Master library
/// </summary>
public static class BasicTests
{
    /// <summary>
    /// Test ModbusMaster creation and basic functionality
    /// </summary>
    public static async Task TestModbusMasterCreation()
    {
        Console.WriteLine("=== Testing ModbusMaster Creation ===");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
                .WithDefaultTimeout(TimeSpan.FromSeconds(5))
                .WithParallelProcessing(true)
                .WithMaxParallelRequests(50)
                .Build();
            
            Console.WriteLine("✓ ModbusMaster created successfully");
            Console.WriteLine($"  Protocol Mode: {modbusMaster.ProtocolMode}");
            Console.WriteLine($"  Default Timeout: {modbusMaster.DefaultTimeout}");
            
            // Test start/stop
            await modbusMaster.StartAsync();
            Console.WriteLine("✓ ModbusMaster started successfully");
            
            await modbusMaster.StopAsync();
            Console.WriteLine("✓ ModbusMaster stopped successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Test Modbus request creation
    /// </summary>
    public static void TestModbusRequestCreation()
    {
        Console.WriteLine("\n=== Testing Modbus Request Creation ===");
        
        try
        {
            // Test read holding registers request
            var request = new ModbusRequest
            {
                TransactionId = 1,
                UnitId = 1,
                FunctionCode = ModbusFunctionCode.ReadHoldingRegisters,
                StartingAddress = 0,
                Quantity = 10
            };
            
            var tcpBytes = request.ToByteArray(ModbusProtocolMode.ModbusTcp);
            Console.WriteLine($"✓ Modbus TCP request created: {tcpBytes.Length} bytes");
            Console.WriteLine($"  Transaction ID: {request.TransactionId}");
            Console.WriteLine($"  Function Code: {request.FunctionCode}");
            Console.WriteLine($"  Start Address: {request.StartingAddress}");
            Console.WriteLine($"  Quantity: {request.Quantity}");
            
            var rtuBytes = request.ToByteArray(ModbusProtocolMode.ModbusRtuOverTcp);
            Console.WriteLine($"✓ Modbus RTU request created: {rtuBytes.Length} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Test Modbus response parsing
    /// </summary>
    public static void TestModbusResponseParsing()
    {
        Console.WriteLine("\n=== Testing Modbus Response Parsing ===");
        
        try
        {
            // Create a mock TCP response for read holding registers
            var mockTcpResponse = new byte[]
            {
                0x00, 0x01, // Transaction ID
                0x00, 0x00, // Protocol ID
                0x00, 0x07, // Length
                0x01,       // Unit ID
                0x03,       // Function Code (Read Holding Registers)
                0x04,       // Byte count
                0x00, 0x0A, // Register 1 value (10)
                0x00, 0x14  // Register 2 value (20)
            };
            
            var response = ModbusResponse.Parse(mockTcpResponse, ModbusProtocolMode.ModbusTcp);
            Console.WriteLine("✓ Modbus TCP response parsed successfully");
            Console.WriteLine($"  Transaction ID: {response.TransactionId}");
            Console.WriteLine($"  Unit ID: {response.UnitId}");
            Console.WriteLine($"  Function Code: {response.FunctionCode}");
            Console.WriteLine($"  Is Exception: {response.IsException}");
            
            var registerValues = response.GetRegisterValues(2);
            Console.WriteLine($"  Register Values: [{string.Join(", ", registerValues)}]");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Test FluentAPI builder
    /// </summary>
    public static void TestFluentApiBuilder()
    {
        Console.WriteLine("\n=== Testing FluentAPI Builder ===");
        
        try
        {
            var builder = new ModbusMasterBuilder()
                .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
                .WithDefaultTimeout(TimeSpan.FromSeconds(10))
                .WithParallelProcessing(true)
                .WithMaxParallelRequests(100)
                .OnDataValueUpdated((sender, e) =>
                {
                    Console.WriteLine($"Data updated: {e.RegisterType} at {e.StartAddress}");
                })
                .OnConnectionStatusChanged((sender, e) =>
                {
                    Console.WriteLine($"Connection status: {e.CurrentStatus}");
                });
            
            Console.WriteLine("✓ FluentAPI builder configured successfully");
            
            var deviceBuilder = builder.AddDevice("***************", 502)
                .PollHoldingRegisters(1, 0, 10, TimeSpan.FromSeconds(1))
                .PollCoils(1, 0, 16, TimeSpan.FromSeconds(2))
                .EndDevice();
            
            Console.WriteLine("✓ Device configuration added successfully");
            
            using var modbusMaster = builder.Build();
            Console.WriteLine("✓ ModbusMaster built successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Test basic read operations (mock)
    /// </summary>
    public static async Task TestBasicReadOperations()
    {
        Console.WriteLine("\n=== Testing Basic Read Operations (Mock) ===");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
                .WithDefaultTimeout(TimeSpan.FromSeconds(5))
                .Build();
            
            await modbusMaster.StartAsync();
            
            // Test read holding registers (will return empty array for now)
            var holdingRegisters = await modbusMaster.ReadHoldingRegistersAsync(
                "***************", 502, 1, 0, 10);
            Console.WriteLine($"✓ Read holding registers: {holdingRegisters.Length} registers");
            
            // Test read coils (will return empty array for now)
            var coils = await modbusMaster.ReadCoilsAsync(
                "***************", 502, 1, 0, 16);
            Console.WriteLine($"✓ Read coils: {coils.Length} coils");
            
            // Test read input registers (will return empty array for now)
            var inputRegisters = await modbusMaster.ReadInputRegistersAsync(
                "***************", 502, 1, 0, 5);
            Console.WriteLine($"✓ Read input registers: {inputRegisters.Length} registers");
            
            // Test read discrete inputs (will return empty array for now)
            var discreteInputs = await modbusMaster.ReadDiscreteInputsAsync(
                "***************", 502, 1, 0, 8);
            Console.WriteLine($"✓ Read discrete inputs: {discreteInputs.Length} inputs");
            
            await modbusMaster.StopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Test basic write operations (mock)
    /// </summary>
    public static async Task TestBasicWriteOperations()
    {
        Console.WriteLine("\n=== Testing Basic Write Operations (Mock) ===");
        
        try
        {
            using var modbusMaster = new ModbusMasterBuilder()
                .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
                .WithDefaultTimeout(TimeSpan.FromSeconds(5))
                .Build();
            
            await modbusMaster.StartAsync();
            
            // Test write single coil
            await modbusMaster.WriteSingleCoilAsync("***************", 502, 1, 0, true);
            Console.WriteLine("✓ Write single coil completed");
            
            // Test write single register
            await modbusMaster.WriteSingleRegisterAsync("***************", 502, 1, 0, 1234);
            Console.WriteLine("✓ Write single register completed");
            
            // Test write multiple coils
            var coilValues = new bool[] { true, false, true, false };
            await modbusMaster.WriteMultipleCoilsAsync("***************", 502, 1, 0, coilValues);
            Console.WriteLine($"✓ Write multiple coils completed: {coilValues.Length} coils");
            
            // Test write multiple registers
            var registerValues = new ushort[] { 100, 200, 300, 400 };
            await modbusMaster.WriteMultipleRegistersAsync("***************", 502, 1, 0, registerValues);
            Console.WriteLine($"✓ Write multiple registers completed: {registerValues.Length} registers");
            
            await modbusMaster.StopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Error: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Run all basic tests
    /// </summary>
    public static async Task RunAllTests()
    {
        Console.WriteLine("=== Ngp.Communication.ModbusTcpMaster Basic Tests ===\n");
        
        try
        {
            await TestModbusMasterCreation();
            TestModbusRequestCreation();
            TestModbusResponseParsing();
            TestFluentApiBuilder();
            await TestBasicReadOperations();
            await TestBasicWriteOperations();
            
            Console.WriteLine("\n=== All Basic Tests Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n=== Test Failed: {ex.Message} ===");
            throw;
        }
    }
}
