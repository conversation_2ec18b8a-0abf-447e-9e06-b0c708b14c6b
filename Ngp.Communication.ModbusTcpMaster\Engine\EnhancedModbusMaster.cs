using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Monitoring;

namespace Ngp.Communication.ModbusTcpMaster.Engine;

/// <summary>
/// Enhanced Modbus TCP Master with parallel processing, polling, and write queue capabilities
/// </summary>
public class EnhancedModbusMaster : IModbusMaster
{
    private readonly ILogger<EnhancedModbusMaster> _logger;
    private readonly TcpConnectionPool _connectionPool;
    private readonly ParallelRequestProcessor _requestProcessor;
    private readonly PollingEngine _pollingEngine;
    private readonly WriteCommandQueue _writeCommandQueue;
    private readonly ModbusMonitoring _monitoring;
    private readonly object _lockObject = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private ushort _nextTransactionId = 1;
    private bool _disposed = false;
    private bool _isStarted = false;
    
    /// <summary>
    /// Protocol mode (TCP or RTU over TCP)
    /// </summary>
    public ModbusProtocolMode ProtocolMode { get; private set; } = ModbusProtocolMode.ModbusTcp;
    
    /// <summary>
    /// Default timeout for requests
    /// </summary>
    public TimeSpan DefaultTimeout { get; private set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Enable or disable parallel processing
    /// </summary>
    public bool ParallelProcessingEnabled 
    { 
        get => _requestProcessor.ParallelProcessingEnabled;
        set => _requestProcessor.ParallelProcessingEnabled = value;
    }
    
    /// <summary>
    /// Maximum concurrent requests
    /// </summary>
    public int MaxConcurrentRequests => _requestProcessor.MaxConcurrentRequests;
    
    /// <summary>
    /// Polling engine for automatic data collection
    /// </summary>
    public PollingEngine PollingEngine => _pollingEngine;
    
    /// <summary>
    /// Write command queue for coordinated write operations
    /// </summary>
    public WriteCommandQueue WriteCommandQueue => _writeCommandQueue;

    /// <summary>
    /// Monitoring system for statistics and health metrics
    /// </summary>
    public ModbusMonitoring Monitoring => _monitoring;
    
    /// <summary>
    /// Event raised when data values are updated
    /// </summary>
    public event EventHandler<DataValueUpdatedEventArgs>? DataValueUpdated;
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Event raised when communication errors occur
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
    
    /// <summary>
    /// Event raised when Modbus exceptions occur
    /// </summary>
    public event EventHandler<ModbusExceptionEventArgs>? ModbusExceptionOccurred;
    
    /// <summary>
    /// Initializes a new instance of the EnhancedModbusMaster class
    /// </summary>
    /// <param name="maxConcurrentRequests">Maximum concurrent requests</param>
    /// <param name="maxPendingWrites">Maximum pending write commands</param>
    /// <param name="logger">Logger instance</param>
    public EnhancedModbusMaster(int maxConcurrentRequests = 50, int maxPendingWrites = 100,
        ILogger<EnhancedModbusMaster>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<EnhancedModbusMaster>.Instance;
        _connectionPool = new TcpConnectionPool();

        // Create monitoring system
        _monitoring = new ModbusMonitoring();

        // Create parallel request processor
        _requestProcessor = new ParallelRequestProcessor(_connectionPool, maxConcurrentRequests);

        // Create polling engine
        _pollingEngine = new PollingEngine(_requestProcessor);

        // Create write command queue
        _writeCommandQueue = new WriteCommandQueue(_requestProcessor, maxPendingWrites);

        // Subscribe to events
        _pollingEngine.DataValueUpdated += OnDataValueUpdated;
        _pollingEngine.PollingError += OnPollingError;
        _writeCommandQueue.WriteCommandCompleted += OnWriteCommandCompleted;
        _writeCommandQueue.WriteCommandFailed += OnWriteCommandFailed;
    }
    
    /// <summary>
    /// Start the enhanced Modbus master engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the start operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(EnhancedModbusMaster));
        
        if (_isStarted)
            return;
        
        _logger.LogInformation("Starting Enhanced Modbus Master engine with {MaxConcurrent} max concurrent requests",
            MaxConcurrentRequests);
        
        // Start polling engine
        _pollingEngine.Start();
        
        _isStarted = true;
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Stop the enhanced Modbus master engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the stop operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || !_isStarted)
            return;
        
        _logger.LogInformation("Stopping Enhanced Modbus Master engine");
        
        // Stop polling engine
        _pollingEngine.Stop();
        
        _cancellationTokenSource.Cancel();
        _isStarted = false;
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Read coils from a Modbus device using parallel processing
    /// </summary>
    public async Task<bool[]> ReadCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} coils from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadCoils,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _requestProcessor.SubmitRequestAsync(request, ipAddress, port, ProtocolMode, 
                RequestPriority.Normal, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetCoilValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read discrete inputs from a Modbus device using parallel processing
    /// </summary>
    public async Task<bool[]> ReadDiscreteInputsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} discrete inputs from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadDiscreteInputs,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _requestProcessor.SubmitRequestAsync(request, ipAddress, port, ProtocolMode, 
                RequestPriority.Normal, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetCoilValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read holding registers from a Modbus device using parallel processing
    /// </summary>
    public async Task<ushort[]> ReadHoldingRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} holding registers from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadHoldingRegisters,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        var startTime = DateTime.UtcNow;

        try
        {
            var response = await _requestProcessor.SubmitRequestAsync(request, ipAddress, port, ProtocolMode,
                RequestPriority.Normal, cancellationToken);

            var responseTime = DateTime.UtcNow - startTime;

            if (response.IsException)
            {
                // Record Modbus exception
                _monitoring.RecordModbusException(ipAddress, port, unitId, request.FunctionCode,
                    response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure);

                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            // Record successful request
            var dataSize = quantity * 2; // 2 bytes per register
            _monitoring.RecordSuccessfulRequest(ipAddress, port, unitId, request.FunctionCode, responseTime, dataSize);

            return response.GetRegisterValues(quantity);
        }
        catch (Exception ex)
        {
            var responseTime = DateTime.UtcNow - startTime;

            // Record failed request
            _monitoring.RecordFailedRequest(ipAddress, port, unitId, request.FunctionCode,
                CommunicationErrorType.NetworkError, ex.Message);

            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read input registers from a Modbus device using parallel processing
    /// </summary>
    public async Task<ushort[]> ReadInputRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} input registers from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadInputRegisters,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _requestProcessor.SubmitRequestAsync(request, ipAddress, port, ProtocolMode, 
                RequestPriority.Normal, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetRegisterValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Write a single coil using the write command queue
    /// </summary>
    public async Task WriteSingleCoilAsync(string ipAddress, int port, byte unitId, ushort address, bool value,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var success = await _writeCommandQueue.QueueWriteSingleCoilAsync(ipAddress, port, unitId, address, value, 
            WriteCommandPriority.Normal, cancellationToken);
        
        if (!success)
        {
            throw new InvalidOperationException("Failed to write single coil");
        }
    }
    
    /// <summary>
    /// Write a single register using the write command queue
    /// </summary>
    public async Task WriteSingleRegisterAsync(string ipAddress, int port, byte unitId, ushort address, ushort value,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var success = await _writeCommandQueue.QueueWriteSingleRegisterAsync(ipAddress, port, unitId, address, value, 
            WriteCommandPriority.Normal, cancellationToken);
        
        if (!success)
        {
            throw new InvalidOperationException("Failed to write single register");
        }
    }
    
    /// <summary>
    /// Write multiple coils using the write command queue
    /// </summary>
    public async Task WriteMultipleCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, bool[] values,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var success = await _writeCommandQueue.QueueWriteMultipleCoilsAsync(ipAddress, port, unitId, startAddress, values, 
            WriteCommandPriority.Normal, cancellationToken);
        
        if (!success)
        {
            throw new InvalidOperationException("Failed to write multiple coils");
        }
    }
    
    /// <summary>
    /// Write multiple registers using the write command queue
    /// </summary>
    public async Task WriteMultipleRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort[] values,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var success = await _writeCommandQueue.QueueWriteMultipleRegistersAsync(ipAddress, port, unitId, startAddress, values, 
            WriteCommandPriority.Normal, cancellationToken);
        
        if (!success)
        {
            throw new InvalidOperationException("Failed to write multiple registers");
        }
    }
    
    /// <summary>
    /// Get next transaction ID
    /// </summary>
    private ushort GetNextTransactionId()
    {
        lock (_lockObject)
        {
            return _nextTransactionId++;
        }
    }
    
    /// <summary>
    /// Handle data value updated events from polling engine
    /// </summary>
    private void OnDataValueUpdated(object? sender, DataValueUpdatedEventArgs e)
    {
        DataValueUpdated?.Invoke(this, e);
    }
    
    /// <summary>
    /// Handle polling error events
    /// </summary>
    private void OnPollingError(object? sender, CommunicationErrorEventArgs e)
    {
        CommunicationError?.Invoke(this, e);
    }
    
    /// <summary>
    /// Handle write command completed events
    /// </summary>
    private void OnWriteCommandCompleted(object? sender, WriteCommandCompletedEventArgs e)
    {
        _logger.LogDebug("Write command completed: {CommandId} {WriteType} to {IpAddress}:{Port} Success:{Success}",
            e.CommandId, e.WriteType, e.IpAddress, e.Port, e.Success);
    }
    
    /// <summary>
    /// Handle write command failed events
    /// </summary>
    private void OnWriteCommandFailed(object? sender, WriteCommandFailedEventArgs e)
    {
        _logger.LogWarning("Write command failed: {CommandId} {WriteType} to {IpAddress}:{Port} Error:{Error}",
            e.CommandId, e.WriteType, e.IpAddress, e.Port, e.Error);
        
        var errorArgs = new CommunicationErrorEventArgs
        {
            IpAddress = e.IpAddress,
            Port = e.Port,
            UnitId = e.UnitId,
            ErrorType = CommunicationErrorType.ProtocolError,
            ErrorMessage = e.Error,
            Exception = e.Exception
        };
        
        CommunicationError?.Invoke(this, errorArgs);
    }
    
    /// <summary>
    /// Get comprehensive statistics
    /// </summary>
    /// <returns>Enhanced master statistics</returns>
    public EnhancedMasterStatistics GetStatistics()
    {
        return new EnhancedMasterStatistics
        {
            ProcessorStatistics = _requestProcessor.GetStatistics(),
            PollingStatistics = _pollingEngine.GetStatistics(),
            WriteQueueStatistics = _writeCommandQueue.GetStatistics(),
            IsStarted = _isStarted,
            ParallelProcessingEnabled = ParallelProcessingEnabled,
            DefaultTimeout = DefaultTimeout,
            ProtocolMode = ProtocolMode
        };
    }
    
    /// <summary>
    /// Dispose the enhanced Modbus master
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;
        
        _logger.LogInformation("Disposing Enhanced Modbus Master");
        
        // Stop if running
        if (_isStarted)
        {
            StopAsync().Wait(TimeSpan.FromSeconds(5));
        }
        
        // Dispose components
        _pollingEngine.Dispose();
        _writeCommandQueue.Dispose();
        _requestProcessor.Dispose();
        _connectionPool.Dispose();
        _cancellationTokenSource.Dispose();
        
        _disposed = true;
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Enhanced master statistics
/// </summary>
public class EnhancedMasterStatistics
{
    public ProcessorStatistics ProcessorStatistics { get; set; } = new();
    public PollingStatistics PollingStatistics { get; set; } = new();
    public WriteQueueStatistics WriteQueueStatistics { get; set; } = new();
    public bool IsStarted { get; set; }
    public bool ParallelProcessingEnabled { get; set; }
    public TimeSpan DefaultTimeout { get; set; }
    public ModbusProtocolMode ProtocolMode { get; set; }
}
