using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Monitoring;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add Swagger services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "Ngp Modbus TCP Master API",
        Version = "v1",
        Description = "Enterprise-grade Modbus TCP Master API for industrial automation",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "Ngp Development Team",
            Email = "<EMAIL>"
        }
    });

    // Add XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // Add examples and better documentation
    c.DescribeAllParametersInCamelCase();
});

// Register Enhanced ModbusMaster as singleton
builder.Services.AddSingleton<EnhancedModbusMaster>(serviceProvider =>
{
    var logger = serviceProvider.GetService<ILogger<EnhancedModbusMaster>>();
    var modbusMaster = new EnhancedModbusMaster(maxConcurrentRequests: 100, maxPendingWrites: 200, logger);

    // Subscribe to events
    modbusMaster.DataValueUpdated += (sender, e) =>
    {
        // Log data value updates
        logger?.LogInformation("Data updated: {IpAddress}:{Port} Unit:{UnitId} {RegisterType} Address:{StartAddress}",
            e.IpAddress, e.Port, e.UnitId, e.RegisterType, e.StartAddress);
    };

    modbusMaster.CommunicationError += (sender, e) =>
    {
        // Log communication errors
        logger?.LogWarning("Communication error: {IpAddress}:{Port} {ErrorType} - {ErrorMessage}",
            e.IpAddress, e.Port, e.ErrorType, e.ErrorMessage);
    };

    modbusMaster.ModbusExceptionOccurred += (sender, e) =>
    {
        // Log Modbus exceptions
        logger?.LogWarning("Modbus exception: {IpAddress}:{Port} Unit:{UnitId} Function:{FunctionCode} Exception:{ExceptionCode}",
            e.IpAddress, e.Port, e.UnitId, e.FunctionCode, e.ExceptionCode);
    };

    return modbusMaster;
});

// Register Modbus Monitoring as singleton
builder.Services.AddSingleton<ModbusMonitoring>();

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Ngp Modbus TCP Master API v1");
        c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
        c.DocumentTitle = "Ngp Modbus TCP Master API Documentation";
        c.DefaultModelsExpandDepth(-1); // Hide models section by default
        c.DisplayRequestDuration();
        c.EnableDeepLinking();
        c.EnableFilter();
        c.ShowExtensions();
    });
}

app.UseHttpsRedirection();

// Add a simple home page with links to documentation
app.MapGet("/", () => Results.Content("""
<!DOCTYPE html>
<html>
<head>
    <title>Ngp Modbus TCP Master API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .api-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .api-link { background: #3498db; color: white; padding: 15px 20px; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s; }
        .api-link:hover { background: #2980b9; }
        .info { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .status { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏭 Ngp Modbus TCP Master API</h1>
        <p>Enterprise-grade Modbus TCP Master API for industrial automation</p>

        <div class="info">
            <p><strong>Status:</strong> <span class="status">✅ Running</span></p>
            <p><strong>Version:</strong> v1.0</p>
            <p><strong>Test Device:</strong> ***************:502</p>
        </div>

        <div class="api-links">
            <a href="/swagger" class="api-link">📚 API Documentation (Swagger UI)</a>
            <a href="/swagger/v1/swagger.json" class="api-link">📄 OpenAPI Specification</a>
            <a href="/modbus/test" class="api-link">🧪 Quick Test</a>
        </div>

        <h2>🚀 Quick Start</h2>
        <p>Use the Swagger UI to explore and test all available Modbus operations:</p>
        <ul>
            <li><strong>Read Operations:</strong> Coils, Discrete Inputs, Holding Registers, Input Registers</li>
            <li><strong>Write Operations:</strong> Single/Multiple Coils, Single/Multiple Registers</li>
            <li><strong>Test Endpoint:</strong> Quick connectivity test to ***************:502</li>
        </ul>

        <h2>📋 Supported Modbus Functions</h2>
        <ul>
            <li>01 - Read Coils</li>
            <li>02 - Read Discrete Inputs</li>
            <li>03 - Read Holding Registers</li>
            <li>04 - Read Input Registers</li>
            <li>05 - Write Single Coil</li>
            <li>06 - Write Single Register</li>
            <li>15 - Write Multiple Coils</li>
            <li>16 - Write Multiple Registers</li>
        </ul>
    </div>
</body>
</html>
""", "text/html"))
.WithName("HomePage")
.WithSummary("API Home Page")
.ExcludeFromDescription();

// Start Enhanced ModbusMaster on application startup
var modbusMaster = app.Services.GetRequiredService<EnhancedModbusMaster>();
await modbusMaster.StartAsync();

// Get monitoring service
var monitoring = app.Services.GetRequiredService<ModbusMonitoring>();

// Graceful shutdown
app.Lifetime.ApplicationStopping.Register(async () =>
{
    await modbusMaster.StopAsync();
    modbusMaster.Dispose();
    monitoring.Dispose();
});

// Modbus TCP Master API endpoints
var modbusApi = app.MapGroup("/modbus").WithTags("Modbus TCP Master");

// Read coils
modbusApi.MapGet("/coils", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadCoilsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadCoils")
.WithSummary("Read coils from a Modbus device")
.WithDescription("Reads discrete output coils (function code 01) from a Modbus TCP device. Returns an array of boolean values representing the coil states.")
.WithOpenApi(operation => new(operation)
{
    Parameters = new List<Microsoft.OpenApi.Models.OpenApiParameter>
    {
        new() { Name = "ip", Description = "IP address of the Modbus device (e.g., ***************)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiString("***************") },
        new() { Name = "port", Description = "TCP port number (typically 502 for Modbus TCP)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(502) },
        new() { Name = "unitId", Description = "Unit identifier (slave address, typically 1)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(1) },
        new() { Name = "startAddress", Description = "Starting coil address (0-based)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(0) },
        new() { Name = "quantity", Description = "Number of coils to read (1-2000)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(16) }
    }
});

// Read discrete inputs
modbusApi.MapGet("/discrete-inputs", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadDiscreteInputsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadDiscreteInputs")
.WithSummary("Read discrete inputs from a Modbus device");

// Read holding registers
modbusApi.MapGet("/holding-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadHoldingRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadHoldingRegisters")
.WithSummary("Read holding registers from a Modbus device")
.WithDescription("Reads holding registers (function code 03) from a Modbus TCP device. Returns an array of 16-bit unsigned integer values.")
.WithOpenApi(operation => new(operation)
{
    Parameters = new List<Microsoft.OpenApi.Models.OpenApiParameter>
    {
        new() { Name = "ip", Description = "IP address of the Modbus device", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiString("***************") },
        new() { Name = "port", Description = "TCP port number (typically 502)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(502) },
        new() { Name = "unitId", Description = "Unit identifier (slave address)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(1) },
        new() { Name = "startAddress", Description = "Starting register address (0-based)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(0) },
        new() { Name = "quantity", Description = "Number of registers to read (1-125)", Required = true, Example = new Microsoft.OpenApi.Any.OpenApiInteger(10) }
    }
});

// Read input registers
modbusApi.MapGet("/input-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadInputRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadInputRegisters")
.WithSummary("Read input registers from a Modbus device");

// Write single coil
modbusApi.MapPost("/coil", async (WriteCoilRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleCoilAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Coil written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleCoil")
.WithSummary("Write a single coil to a Modbus device");

// Write single register
modbusApi.MapPost("/register", async (WriteRegisterRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleRegisterAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Register written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleRegister")
.WithSummary("Write a single register to a Modbus device");

// Write multiple coils
modbusApi.MapPost("/coils", async (WriteMultipleCoilsRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleCoilsAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = $"{request.Values.Length} coils written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleCoils")
.WithSummary("Write multiple coils to a Modbus device");

// Write multiple registers
modbusApi.MapPost("/registers", async (WriteMultipleRegistersRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleRegistersAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = $"{request.Values.Length} registers written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleRegisters")
.WithSummary("Write multiple registers to a Modbus device");

// Test endpoint for ***************:502
modbusApi.MapGet("/test", async () =>
{
    try
    {
        var testIp = "***************";
        var testPort = 502;
        byte unitId = 1;

        // Test read holding registers
        var registers = await modbusMaster.ReadHoldingRegistersAsync(testIp, testPort, unitId, 0, 10);

        return Results.Ok(new
        {
            success = true,
            message = "Test completed successfully",
            device = $"{testIp}:{testPort}",
            unitId,
            registersRead = registers.Length
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("TestModbusConnection")
.WithSummary("Test Modbus connection to ***************:502")
.WithDescription("Performs a quick connectivity test to the default test device at ***************:502. Reads 10 holding registers starting from address 0.");

// Enhanced features endpoints
var enhancedApi = app.MapGroup("/enhanced").WithTags("Enhanced Features");

// Get system statistics
enhancedApi.MapGet("/statistics", () =>
{
    var stats = modbusMaster.GetStatistics();
    return Results.Ok(stats);
})
.WithName("GetSystemStatistics")
.WithSummary("Get comprehensive system statistics")
.WithDescription("Returns detailed statistics about parallel processing, polling, write queue, and overall system performance.");

// Get monitoring report
enhancedApi.MapGet("/monitoring", () =>
{
    var report = monitoring.GetMonitoringReport();
    return Results.Ok(report);
})
.WithName("GetMonitoringReport")
.WithSummary("Get detailed monitoring report")
.WithDescription("Returns comprehensive monitoring data including device statistics, function code statistics, and system health metrics.");

// Start polling for a device
enhancedApi.MapPost("/polling/start", (StartPollingRequest request) =>
{
    try
    {
        modbusMaster.PollingEngine.AddHoldingRegisterPolling(
            request.IpAddress,
            request.Port,
            request.UnitId,
            request.StartAddress,
            request.Count,
            TimeSpan.FromMilliseconds(request.IntervalMs),
            request.GroupName);

        return Results.Ok(new { success = true, message = "Polling started successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("StartPolling")
.WithSummary("Start automatic polling for a device")
.WithDescription("Starts automatic polling of holding registers for the specified device and address range.");

// Stop polling for a group
enhancedApi.MapDelete("/polling/{groupName}", (string groupName) =>
{
    try
    {
        var removed = modbusMaster.PollingEngine.RemovePollingGroup(groupName);
        if (removed)
        {
            return Results.Ok(new { success = true, message = "Polling stopped successfully" });
        }
        else
        {
            return Results.NotFound(new { success = false, error = "Polling group not found" });
        }
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("StopPolling")
.WithSummary("Stop automatic polling for a group")
.WithDescription("Stops automatic polling for the specified polling group.");

// Get polling statistics
enhancedApi.MapGet("/polling/statistics", () =>
{
    var stats = modbusMaster.PollingEngine.GetStatistics();
    return Results.Ok(stats);
})
.WithName("GetPollingStatistics")
.WithSummary("Get polling engine statistics")
.WithDescription("Returns statistics about the polling engine including active groups and performance metrics.");

app.Run();

// Request models
record WriteCoilRequest(string Ip, int Port, byte UnitId, ushort Address, bool Value);
record WriteRegisterRequest(string Ip, int Port, byte UnitId, ushort Address, ushort Value);
record WriteMultipleCoilsRequest(string Ip, int Port, byte UnitId, ushort StartAddress, bool[] Values);
record WriteMultipleRegistersRequest(string Ip, int Port, byte UnitId, ushort StartAddress, ushort[] Values);
record StartPollingRequest(string IpAddress, int Port, byte UnitId, ushort StartAddress, ushort Count, int IntervalMs, string? GroupName);
