using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Fluent;

/// <summary>
/// Fluent API builder for ModbusMaster configuration
/// </summary>
public class ModbusMasterBuilder
{
    private readonly List<EventHandler<DataValueUpdatedEventArgs>> _dataValueUpdatedHandlers = new();
    private readonly List<EventHandler<ConnectionStatusChangedEventArgs>> _connectionStatusChangedHandlers = new();
    private readonly List<EventHandler<CommunicationErrorEventArgs>> _communicationErrorHandlers = new();
    private readonly List<EventHandler<ModbusExceptionEventArgs>> _modbusExceptionHandlers = new();
    private ModbusProtocolMode _protocolMode = ModbusProtocolMode.ModbusTcp;
    private TimeSpan _defaultTimeout = TimeSpan.FromSeconds(5);
    private TimeSpan _gapTime = TimeSpan.FromMilliseconds(10);
    private bool _parallelProcessing = false;
    private int _maxParallelRequests = 50;
    private ILogger<ModbusMaster>? _logger;
    
    /// <summary>
    /// Initializes a new instance of the ModbusMasterBuilder class
    /// </summary>
    public ModbusMasterBuilder()
    {
        // ModbusMaster will be created in Build() method
    }
    
    /// <summary>
    /// Set the protocol mode
    /// </summary>
    /// <param name="protocolMode">Protocol mode to use</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithProtocolMode(ModbusProtocolMode protocolMode)
    {
        _protocolMode = protocolMode;
        return this;
    }
    
    /// <summary>
    /// Set the default timeout for requests
    /// </summary>
    /// <param name="timeout">Default timeout</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithDefaultTimeout(TimeSpan timeout)
    {
        _defaultTimeout = timeout;
        return this;
    }
    
    /// <summary>
    /// Set the gap time between requests
    /// </summary>
    /// <param name="gapTime">Gap time</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithGapTime(TimeSpan gapTime)
    {
        _gapTime = gapTime;
        return this;
    }
    
    /// <summary>
    /// Enable or disable parallel processing
    /// </summary>
    /// <param name="enabled">Whether to enable parallel processing</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithParallelProcessing(bool enabled)
    {
        _parallelProcessing = enabled;
        return this;
    }
    
    /// <summary>
    /// Set the maximum number of parallel requests
    /// </summary>
    /// <param name="maxRequests">Maximum parallel requests</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithMaxParallelRequests(int maxRequests)
    {
        _maxParallelRequests = maxRequests;
        return this;
    }
    
    /// <summary>
    /// Set the logger instance
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithLogger(ILogger<ModbusMaster> logger)
    {
        _logger = logger;
        return this;
    }
    
    /// <summary>
    /// Configure connection settings
    /// </summary>
    /// <param name="connectionTimeout">Connection timeout</param>
    /// <param name="receiveTimeout">Receive timeout</param>
    /// <param name="maxConcurrentRequestsPerConnection">Max concurrent requests per connection</param>
    /// <param name="maxReconnectAttempts">Max reconnect attempts</param>
    /// <param name="reconnectDelay">Reconnect delay</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithConnectionSettings(
        TimeSpan connectionTimeout,
        TimeSpan receiveTimeout,
        int maxConcurrentRequestsPerConnection,
        int maxReconnectAttempts,
        TimeSpan reconnectDelay)
    {
        // TODO: Store connection settings
        return this;
    }
    
    /// <summary>
    /// Configure polling settings
    /// </summary>
    /// <param name="pollingCheckInterval">Polling check interval</param>
    /// <param name="maxConcurrentPolling">Max concurrent polling operations</param>
    /// <param name="enableChangeDetection">Enable change detection</param>
    /// <param name="maxRegisterRangeSize">Max register range size</param>
    /// <param name="maxCoilRangeSize">Max coil range size</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder WithPollingSettings(
        TimeSpan pollingCheckInterval,
        int maxConcurrentPolling,
        bool enableChangeDetection,
        ushort maxRegisterRangeSize = 100,
        ushort maxCoilRangeSize = 1000)
    {
        // TODO: Store polling settings
        return this;
    }
    
    /// <summary>
    /// Subscribe to data value updated events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnDataValueUpdated(EventHandler<DataValueUpdatedEventArgs> handler)
    {
        _dataValueUpdatedHandlers.Add(handler);
        return this;
    }

    /// <summary>
    /// Subscribe to connection status changed events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnConnectionStatusChanged(EventHandler<ConnectionStatusChangedEventArgs> handler)
    {
        _connectionStatusChangedHandlers.Add(handler);
        return this;
    }

    /// <summary>
    /// Subscribe to communication error events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnCommunicationError(EventHandler<CommunicationErrorEventArgs> handler)
    {
        _communicationErrorHandlers.Add(handler);
        return this;
    }

    /// <summary>
    /// Subscribe to Modbus exception events
    /// </summary>
    /// <param name="handler">Event handler</param>
    /// <returns>Builder instance</returns>
    public ModbusMasterBuilder OnModbusException(EventHandler<ModbusExceptionEventArgs> handler)
    {
        _modbusExceptionHandlers.Add(handler);
        return this;
    }
    
    /// <summary>
    /// Add a device configuration
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder AddDevice(string ipAddress, int port = 502)
    {
        return new DeviceConfigurationBuilder(this, ipAddress, port);
    }
    
    /// <summary>
    /// Build and configure the ModbusMaster
    /// </summary>
    /// <returns>Configured ModbusMaster instance</returns>
    public ModbusMaster Build()
    {
        var modbusMaster = new ModbusMaster(_logger);

        // Subscribe to events
        foreach (var handler in _dataValueUpdatedHandlers)
        {
            modbusMaster.DataValueUpdated += handler;
        }

        foreach (var handler in _connectionStatusChangedHandlers)
        {
            modbusMaster.ConnectionStatusChanged += handler;
        }

        foreach (var handler in _communicationErrorHandlers)
        {
            modbusMaster.CommunicationError += handler;
        }

        foreach (var handler in _modbusExceptionHandlers)
        {
            modbusMaster.ModbusExceptionOccurred += handler;
        }

        // TODO: Apply all configuration settings to the ModbusMaster instance
        return modbusMaster;
    }
}

/// <summary>
/// Device configuration builder for fluent API
/// </summary>
public class DeviceConfigurationBuilder
{
    private readonly ModbusMasterBuilder _parentBuilder;
    private readonly string _ipAddress;
    private readonly int _port;
    
    /// <summary>
    /// Initializes a new instance of the DeviceConfigurationBuilder class
    /// </summary>
    /// <param name="parentBuilder">Parent builder</param>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    public DeviceConfigurationBuilder(ModbusMasterBuilder parentBuilder, string ipAddress, int port)
    {
        _parentBuilder = parentBuilder;
        _ipAddress = ipAddress;
        _port = port;
    }
    
    /// <summary>
    /// Configure polling for holding registers
    /// </summary>
    /// <param name="unitId">Unit ID</param>
    /// <param name="startAddress">Start address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollHoldingRegisters(byte unitId, ushort startAddress, ushort count, TimeSpan pollingInterval)
    {
        // TODO: Store polling configuration
        return this;
    }
    
    /// <summary>
    /// Configure polling for coils
    /// </summary>
    /// <param name="unitId">Unit ID</param>
    /// <param name="startAddress">Start address</param>
    /// <param name="count">Number of coils</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollCoils(byte unitId, ushort startAddress, ushort count, TimeSpan pollingInterval)
    {
        // TODO: Store polling configuration
        return this;
    }
    
    /// <summary>
    /// Configure polling for input registers
    /// </summary>
    /// <param name="unitId">Unit ID</param>
    /// <param name="startAddress">Start address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="pollingInterval">Polling interval</param>
    /// <returns>Device configuration builder</returns>
    public DeviceConfigurationBuilder PollInputRegisters(byte unitId, ushort startAddress, ushort count, TimeSpan pollingInterval)
    {
        // TODO: Store polling configuration
        return this;
    }
    
    /// <summary>
    /// Complete device configuration and return to parent builder
    /// </summary>
    /// <returns>Parent ModbusMasterBuilder</returns>
    public ModbusMasterBuilder EndDevice()
    {
        return _parentBuilder;
    }
}
