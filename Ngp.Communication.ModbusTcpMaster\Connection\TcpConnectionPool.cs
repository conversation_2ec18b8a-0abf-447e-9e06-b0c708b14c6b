using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Connection;

/// <summary>
/// Manages a pool of TCP connections to Modbus devices
/// </summary>
public class TcpConnectionPool : IDisposable
{
    private readonly ILogger<TcpConnectionPool> _logger;
    private readonly ConcurrentDictionary<string, TcpConnection> _connections = new();
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();
    private bool _disposed = false;
    
    /// <summary>
    /// Connection idle timeout before cleanup
    /// </summary>
    public TimeSpan IdleTimeout { get; set; } = TimeSpan.FromMinutes(5);
    
    /// <summary>
    /// Cleanup interval
    /// </summary>
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(1);
    
    /// <summary>
    /// Maximum number of concurrent connections
    /// </summary>
    public int MaxConnections { get; set; } = 1000;
    
    /// <summary>
    /// Current number of active connections
    /// </summary>
    public int ActiveConnections => _connections.Count;
    
    /// <summary>
    /// Initializes a new instance of the TcpConnectionPool class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public TcpConnectionPool(ILogger<TcpConnectionPool>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<TcpConnectionPool>.Instance;
        
        // Start cleanup timer
        _cleanupTimer = new Timer(CleanupIdleConnections, null, CleanupInterval, CleanupInterval);
    }
    
    /// <summary>
    /// Get or create a connection to the specified endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>TCP connection</returns>
    public async Task<TcpConnection> GetConnectionAsync(string ipAddress, int port, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(TcpConnectionPool));
        
        var endpointId = $"{ipAddress}:{port}";
        
        // Try to get existing connection
        if (_connections.TryGetValue(endpointId, out var existingConnection))
        {
            if (existingConnection.IsConnected)
            {
                _logger.LogTrace("Reusing existing connection to {EndpointId}", endpointId);
                return existingConnection;
            }
            else
            {
                // Remove disconnected connection
                _connections.TryRemove(endpointId, out _);
                existingConnection.Dispose();
            }
        }
        
        // Check connection limit
        if (_connections.Count >= MaxConnections)
        {
            _logger.LogWarning("Maximum connection limit ({MaxConnections}) reached", MaxConnections);
            throw new InvalidOperationException($"Maximum connection limit ({MaxConnections}) reached");
        }
        
        // Create new connection
        _logger.LogDebug("Creating new connection to {EndpointId}", endpointId);
        
        var connection = new TcpConnection(ipAddress, port);
        
        try
        {
            await connection.ConnectAsync(cancellationToken);
            
            // Add to pool
            _connections.TryAdd(endpointId, connection);
            
            _logger.LogInformation("Added new connection to pool: {EndpointId} (Total: {Count})", 
                endpointId, _connections.Count);
            
            return connection;
        }
        catch
        {
            connection.Dispose();
            throw;
        }
    }
    
    /// <summary>
    /// Send a request using the connection pool
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="request">Modbus request</param>
    /// <param name="protocolMode">Protocol mode</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Modbus response</returns>
    public async Task<ModbusResponse> SendRequestAsync(string ipAddress, int port, ModbusRequest request, 
        ModbusProtocolMode protocolMode, CancellationToken cancellationToken = default)
    {
        var connection = await GetConnectionAsync(ipAddress, port, cancellationToken);
        return await connection.SendRequestAsync(request, protocolMode, cancellationToken);
    }
    
    /// <summary>
    /// Remove a connection from the pool
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    public void RemoveConnection(string ipAddress, int port)
    {
        var endpointId = $"{ipAddress}:{port}";
        
        if (_connections.TryRemove(endpointId, out var connection))
        {
            _logger.LogDebug("Removing connection from pool: {EndpointId}", endpointId);
            connection.Dispose();
        }
    }
    
    /// <summary>
    /// Get connection statistics
    /// </summary>
    /// <returns>Connection statistics</returns>
    public ConnectionPoolStatistics GetStatistics()
    {
        var connectedCount = 0;
        var disconnectedCount = 0;
        var endpoints = new List<string>();
        
        foreach (var kvp in _connections)
        {
            endpoints.Add(kvp.Key);
            if (kvp.Value.IsConnected)
                connectedCount++;
            else
                disconnectedCount++;
        }
        
        return new ConnectionPoolStatistics
        {
            TotalConnections = _connections.Count,
            ConnectedConnections = connectedCount,
            DisconnectedConnections = disconnectedCount,
            Endpoints = endpoints.ToArray(),
            MaxConnections = MaxConnections,
            IdleTimeout = IdleTimeout
        };
    }
    
    /// <summary>
    /// Cleanup idle connections
    /// </summary>
    /// <param name="state">Timer state</param>
    private void CleanupIdleConnections(object? state)
    {
        if (_disposed)
            return;
        
        try
        {
            var now = DateTime.UtcNow;
            var connectionsToRemove = new List<string>();
            
            foreach (var kvp in _connections)
            {
                var connection = kvp.Value;
                var timeSinceLastActivity = now - connection.LastActivity;
                
                if (!connection.IsConnected || timeSinceLastActivity > IdleTimeout)
                {
                    connectionsToRemove.Add(kvp.Key);
                }
            }
            
            foreach (var endpointId in connectionsToRemove)
            {
                if (_connections.TryRemove(endpointId, out var connection))
                {
                    _logger.LogDebug("Cleaning up idle connection: {EndpointId}", endpointId);
                    connection.Dispose();
                }
            }
            
            if (connectionsToRemove.Count > 0)
            {
                _logger.LogInformation("Cleaned up {Count} idle connections (Remaining: {Remaining})", 
                    connectionsToRemove.Count, _connections.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection cleanup");
        }
    }
    
    /// <summary>
    /// Dispose all connections and cleanup resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;
        
        _logger.LogInformation("Disposing connection pool with {Count} connections", _connections.Count);
        
        _cleanupTimer?.Dispose();
        
        // Dispose all connections
        foreach (var connection in _connections.Values)
        {
            try
            {
                connection.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error disposing connection");
            }
        }
        
        _connections.Clear();
        _disposed = true;
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Connection pool statistics
/// </summary>
public class ConnectionPoolStatistics
{
    /// <summary>
    /// Total number of connections in the pool
    /// </summary>
    public int TotalConnections { get; set; }
    
    /// <summary>
    /// Number of connected connections
    /// </summary>
    public int ConnectedConnections { get; set; }
    
    /// <summary>
    /// Number of disconnected connections
    /// </summary>
    public int DisconnectedConnections { get; set; }
    
    /// <summary>
    /// List of endpoint identifiers
    /// </summary>
    public string[] Endpoints { get; set; } = Array.Empty<string>();
    
    /// <summary>
    /// Maximum allowed connections
    /// </summary>
    public int MaxConnections { get; set; }
    
    /// <summary>
    /// Idle timeout setting
    /// </summary>
    public TimeSpan IdleTimeout { get; set; }
}
