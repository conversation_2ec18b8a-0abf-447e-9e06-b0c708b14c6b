# Ngp.Communication.ModbusTcpMaster 開發任務清單

## 專案概述
基於需求文件建立企業級 Modbus TCP Master 庫，使用 .NET 9 開發，支援高性能平行化處理，可連接 1000+ 台 Slave 裝置。

## 開發階段規劃

### 階段 1: 核心架構與基礎設施 (優先級: 高)

#### 1.1 專案結構重構 [複雜度: 中] [預估: 4小時] ✅ 已完成
- [x] 移除預設的 Class1.cs
- [x] 建立核心資料夾結構:
  - [x] `/Engine` - 核心引擎
  - [ ] `/Protocol` - Modbus 協定實作
  - [ ] `/Connection` - TCP 連線管理
  - [x] `/Events` - 事件系統
  - [x] `/Models` - 資料模型
  - [x] `/Fluent` - FluentAPI 介面
  - [ ] `/Monitoring` - 狀態監控
  - [ ] `/Utils` - 工具類別
  - [ ] `/Tests` - 單元測試
  - [ ] `/Examples` - 使用範例

#### 1.2 依賴套件配置 [複雜度: 低] [預估: 1小時] ✅ 已完成
- [x] 更新 .csproj 檔案，加入必要的 NuGet 套件:
  - [x] Microsoft.Extensions.Logging
  - [x] Microsoft.Extensions.DependencyInjection
  - [x] Microsoft.Extensions.Hosting
  - [x] System.Threading.Channels
  - [x] System.IO.Pipelines (高性能 TCP)

#### 1.3 核心介面定義 [複雜度: 中] [預估: 3小時] ✅ 已完成
- [x] `IModbusMaster` - 主要介面
- [ ] `IModbusConnection` - 連線管理介面
- [ ] `IModbusProtocol` - 協定處理介面
- [ ] `IModbusEventEngine` - 事件引擎介面
- [ ] `IModbusMonitoring` - 監控介面
- [ ] `IModbusPollingEngine` - 輪詢引擎介面

### 階段 2: Modbus 協定實作 (優先級: 高)

#### 2.1 Modbus 協定核心 [複雜度: 高] [預估: 8小時] 🔄 進行中
- [x] `ModbusRequest` - Modbus 請求結構
- [x] `ModbusResponse` - Modbus 回應結構
- [x] `ModbusFunctionCode` - 功能碼枚舉
- [x] `ModbusExceptionCode` - 錯誤碼處理
- [ ] `ModbusProtocolHandler` - 協定處理器
- [x] 實作所有標準 Modbus 功能碼:
  - [x] 01 - Read Coils
  - [x] 02 - Read Discrete Inputs
  - [x] 03 - Read Holding Registers
  - [x] 04 - Read Input Registers
  - [x] 05 - Write Single Coil
  - [x] 06 - Write Single Register
  - [x] 15 - Write Multiple Coils
  - [x] 16 - Write Multiple Registers

#### 2.2 協定模式支援 [複雜度: 中] [預估: 4小時]
- [ ] `ModbusTcpProtocol` - Modbus TCP 實作
- [ ] `ModbusRtuOverTcpProtocol` - Modbus RTU over TCP 實作
- [ ] 協定模式切換機制
- [ ] RTU over TCP 的序列化限制 (一次一組問答)

#### 2.3 資料型別轉換 [複雜度: 中] [預估: 3小時]
- [ ] `DataTypeConverter` - 資料型別轉換器
- [ ] 支援不同 Endian 排列組合
- [ ] 支援常見資料型別:
  - [ ] Int16, UInt16, Int32, UInt32
  - [ ] Float, Double
  - [ ] Boolean, String
  - [ ] 自訂型別轉換

### 階段 3: TCP 連線管理 (優先級: 高)

#### 3.1 高性能 TCP 連線池 [複雜度: 高] [預估: 10小時]
- [ ] `TcpConnectionPool` - 連線池管理
- [ ] `TcpConnection` - 單一 TCP 連線
- [ ] `ConnectionEndpoint` - 端點管理 (IP:Port)
- [ ] 連線重用機制 (同 IP:Port 使用單一連線)
- [ ] 支援多實例連向同一端點 (不合併連線)
- [ ] 連線狀態監控
- [ ] 自動重連機制

#### 3.2 連線設定與管理 [複雜度: 中] [預估: 4小時]
- [ ] `ConnectionSettings` - 連線設定模型
- [ ] 可調整的 Timeout 設定
- [ ] 可調整的重連參數
- [ ] 連線生命週期管理
- [ ] Graceful shutdown 機制

#### 3.3 Socket 資源管理 [複雜度: 中] [預估: 3小時]
- [ ] Socket 資源正確回收
- [ ] 異常情況下的連線清理
- [ ] 避免 Socket 耗盡問題
- [ ] 連線洩漏偵測

### 階段 4: 平行化處理引擎 (優先級: 高)

#### 4.1 平行化請求處理 [複雜度: 高] [預估: 8小時]
- [ ] `ParallelRequestProcessor` - 平行請求處理器
- [ ] Channel-based 訊息佇列
- [ ] 可設定的並發請求數量
- [ ] 請求優先級管理
- [ ] 平行化開關控制

#### 4.2 智慧輪詢引擎 [複雜度: 高] [預估: 10小時]
- [ ] `PollingEngine` - 輪詢引擎
- [ ] 自動分割過長的暫存器範圍
- [ ] 智慧範圍最佳化
- [ ] 可設定的輪詢間隔
- [ ] 變更偵測機制
- [ ] 平行化輪詢處理

#### 4.3 動態寫入插入 [複雜度: 高] [預估: 6小時]
- [ ] `WriteCommandQueue` - 寫入指令佇列
- [ ] 動態插入寫入指令
- [ ] 輪詢與寫入的協調機制
- [ ] 寫入指令優先級處理

### 階段 5: 事件系統 (優先級: 中)

#### 5.1 事件引擎核心 [複雜度: 中] [預估: 5小時]
- [ ] `ModbusEventEngine` - 事件引擎
- [ ] 平行化事件分發
- [ ] 事件訂閱管理
- [ ] Thread-safe 事件處理

#### 5.2 事件模型定義 [複雜度: 低] [預估: 2小時] ✅ 已完成
- [x] `DataValueUpdatedEventArgs` - 數值更新事件
- [x] `ConnectionStatusChangedEventArgs` - 連線狀態變更事件
- [x] `CommunicationErrorEventArgs` - 通訊錯誤事件
- [x] `ModbusExceptionEventArgs` - Modbus 例外事件

### 階段 6: FluentAPI 設計 (優先級: 中)

#### 6.1 FluentAPI 建構器 [複雜度: 中] [預估: 6小時] 🔄 進行中
- [x] `ModbusMasterBuilder` - 主要建構器
- [x] `DeviceConfigurationBuilder` - 裝置設定建構器
- [ ] `PollingConfigurationBuilder` - 輪詢設定建構器
- [ ] `ConnectionConfigurationBuilder` - 連線設定建構器

#### 6.2 設定模型 [複雜度: 低] [預估: 2小時]
- [ ] `ModbusMasterConfiguration` - 主要設定
- [ ] `DeviceConfiguration` - 裝置設定
- [ ] `PollingConfiguration` - 輪詢設定
- [ ] `ConnectionConfiguration` - 連線設定

### 階段 7: 狀態監控系統 (優先級: 中)

#### 7.1 監控介面實作 [複雜度: 中] [預估: 5小時]
- [ ] `ModbusMonitoring` - 監控主類別
- [ ] 連線統計資訊
- [ ] 輪詢效能統計
- [ ] 錯誤統計資訊
- [ ] 即時狀態查詢

#### 7.2 效能指標收集 [複雜度: 中] [預估: 3小時]
- [ ] 回應時間統計
- [ ] 輪詢次數統計
- [ ] 寫入操作統計
- [ ] 錯誤率統計

### 階段 8: 日誌系統整合 (優先級: 低)

#### 8.1 ILogger 整合 [複雜度: 低] [預估: 2小時]
- [ ] 移除所有 Console.WriteLine
- [ ] 整合 Microsoft.Extensions.Logging
- [ ] 設定適當的日誌等級
- [ ] 結構化日誌訊息

### 階段 9: 測試與驗證 (優先級: 高)

#### 9.1 單元測試 [複雜度: 中] [預估: 8小時]
- [ ] 協定處理器測試
- [ ] 連線管理測試
- [ ] 平行化處理測試
- [ ] 事件系統測試
- [ ] FluentAPI 測試

#### 9.2 整合測試 [複雜度: 中] [預估: 6小時]
- [ ] 使用 192.168.254.182:502 進行 ModbusTCP 測試
- [ ] 輪詢功能測試
- [ ] 寫入功能測試
- [ ] 錯誤處理測試
- [ ] 效能測試

#### 9.3 API 整合測試 [複雜度: 低] [預估: 3小時]
- [ ] Minimal API 端點測試
- [ ] 與主 Ngp 專案整合測試
- [ ] 端到端功能驗證

### 階段 10: 文件與範例 (優先級: 低)

#### 10.1 API 文件 [複雜度: 低] [預估: 3小時]
- [ ] XML 註解完善
- [ ] README 更新
- [ ] API 參考文件

#### 10.2 使用範例 [複雜度: 低] [預估: 2小時]
- [ ] 基本使用範例
- [ ] 進階設定範例
- [ ] 效能最佳化範例

## 總預估工作量
- **總計**: 約 100 小時
- **核心功能**: 約 70 小時
- **測試驗證**: 約 20 小時
- **文件範例**: 約 10 小時

## 開發順序建議
1. 階段 1 → 階段 2 → 階段 3 (建立基礎)
2. 階段 4 → 階段 5 (核心功能)
3. 階段 6 → 階段 7 (使用者介面)
4. 階段 8 → 階段 9 (完善與測試)
5. 階段 10 (文件)

## 風險評估
- **高風險**: 平行化處理的複雜度和效能最佳化
- **中風險**: TCP 連線池的穩定性和資源管理
- **低風險**: FluentAPI 設計和事件系統

## 成功標準
- [ ] 能穩定連接 1000+ 台 Slave 裝置
- [ ] 支援所有標準 Modbus 功能碼
- [ ] 通過 192.168.254.182:502 的完整功能測試
- [ ] 與主 Ngp 專案無縫整合
- [ ] 符合企業級穩定性要求
