using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Communication;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Data;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Core;

/// <summary>
/// Event handling methods for ModbusMaster
/// </summary>
public sealed partial class ModbusMaster
{
    /// <summary>
    /// Processes write commands from the queue
    /// </summary>
    private async Task ProcessWriteCommandsAsync()
    {
        _logger.LogInformation("Write command processing started");

        try
        {
            await foreach (var writeCommand in _writeCommandReader.ReadAllAsync(_cancellationTokenSource.Token))
            {
                if (writeCommand.IsExpired)
                {
                    _logger.LogWarning("Write command {OperationId} has expired, skipping", writeCommand.Id);
                    _queuedWriteCommands.TryRemove(writeCommand.Id, out _);
                    continue;
                }

                await ExecuteWriteCommandAsync(writeCommand);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when shutting down
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in write command processing loop");
        }

        _logger.LogInformation("Write command processing stopped");
    }

    /// <summary>
    /// Executes a write command
    /// </summary>
    /// <param name="writeCommand">Write command to execute</param>
    private async Task ExecuteWriteCommandAsync(WriteCommand writeCommand)
    {
        var startTime = DateTimeOffset.UtcNow;
        Exception? error = null;
        var retryCount = 0;

        try
        {
            while (retryCount <= writeCommand.MaxRetryAttempts)
            {
                try
                {
                    await ExecuteWriteCommandInternalAsync(writeCommand);
                    break; // Success
                }
                catch (Exception ex)
                {
                    error = ex;
                    retryCount++;

                    if (retryCount <= writeCommand.MaxRetryAttempts)
                    {
                        _logger.LogWarning(ex, "Write command {OperationId} failed, retry {Retry}/{MaxRetries}",
                            writeCommand.Id, retryCount, writeCommand.MaxRetryAttempts);
                        
                        await Task.Delay(TimeSpan.FromMilliseconds(500 * retryCount), _cancellationTokenSource.Token);
                    }
                }
            }
        }
        catch (OperationCanceledException)
        {
            error = new OperationCanceledException("Write operation was cancelled");
        }
        finally
        {
            _queuedWriteCommands.TryRemove(writeCommand.Id, out _);

            var duration = DateTimeOffset.UtcNow - startTime;
            var eventArgs = error == null
                ? new WriteOperationCompletedEventArgs(writeCommand.Id, writeCommand.Endpoint, writeCommand, duration)
                : new WriteOperationCompletedEventArgs(writeCommand.Id, writeCommand.Endpoint, writeCommand, error, duration);

            OnWriteOperationCompleted(eventArgs);
        }
    }

    /// <summary>
    /// Executes a write command internally
    /// </summary>
    /// <param name="writeCommand">Write command to execute</param>
    private async Task ExecuteWriteCommandInternalAsync(WriteCommand writeCommand)
    {
        var communicator = GetCommunicator(writeCommand.Endpoint);
        
        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            SlaveId = writeCommand.SlaveId,
            FunctionCode = writeCommand.FunctionCode,
            Data = writeCommand.Data,
            Timeout = writeCommand.Timeout,
            CancellationToken = _cancellationTokenSource.Token
        };

        var response = await communicator.SendRequestAsync(request);
        ValidateResponse(response, request);

        _logger.LogDebug("Write command {OperationId} executed successfully", writeCommand.Id);
    }

    /// <summary>
    /// Handles connection state changed events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
    {
        try
        {
            _logger.LogInformation("Connection state changed for endpoint {Endpoint}: {PreviousState} -> {NewState}",
                e.Endpoint, e.PreviousState, e.NewState);

            ConnectionStateChanged?.Invoke(this, e);

            // Notify event handlers
            NotifyEventHandlersAsync(handler => handler.HandleConnectionStateChangedAsync(e, _cancellationTokenSource.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling connection state changed event");
        }
    }

    /// <summary>
    /// Handles communication error events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnCommunicationError(object? sender, ModbusCommunicationErrorEventArgs e)
    {
        try
        {
            _logger.LogError(e.Error, "Communication error on endpoint {Endpoint}", e.Endpoint);

            CommunicationError?.Invoke(this, e);

            // Notify event handlers
            NotifyEventHandlersAsync(handler => handler.HandleCommunicationErrorAsync(e, _cancellationTokenSource.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling communication error event");
        }
    }

    /// <summary>
    /// Handles request sent events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnRequestSent(object? sender, ModbusRequestSentEventArgs e)
    {
        try
        {
            _logger.LogTrace("Request sent to endpoint {Endpoint}: {FunctionCode} for slave {SlaveId}",
                e.Endpoint, e.Request.FunctionCode, e.Request.SlaveId);

            _monitor.RecordRequestSent(e.Endpoint, e.Request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling request sent event");
        }
    }

    /// <summary>
    /// Handles response received events
    /// </summary>
    /// <param name="sender">Event sender</param>
    /// <param name="e">Event arguments</param>
    private void OnResponseReceived(object? sender, ModbusResponseReceivedEventArgs e)
    {
        try
        {
            _logger.LogTrace("Response received from endpoint {Endpoint}: {FunctionCode} for slave {SlaveId}, RTT: {RoundTripTime}ms",
                e.Endpoint, e.Response.FunctionCode, e.Response.SlaveId, e.Response.RoundTripTime?.TotalMilliseconds);

            _monitor.RecordResponseReceived(e.Endpoint, e.Request, e.Response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling response received event");
        }
    }

    /// <summary>
    /// Raises the RegisterValueUpdated event
    /// </summary>
    /// <param name="eventArgs">Event arguments</param>
    private void OnRegisterValueUpdated(RegisterValueUpdatedEventArgs eventArgs)
    {
        try
        {
            if (eventArgs.HasChanged)
            {
                _logger.LogTrace("Register value updated: {Endpoint} slave {SlaveId} {RegisterType}:{Address} = {NewValue}",
                    eventArgs.Endpoint, eventArgs.SlaveId, eventArgs.RegisterType, eventArgs.Address, 
                    eventArgs.NewValue.IsValid ? "Valid" : "Invalid");
            }

            RegisterValueUpdated?.Invoke(this, eventArgs);

            // Notify event handlers
            NotifyEventHandlersAsync(handler => handler.HandleRegisterValueUpdatedAsync(eventArgs, _cancellationTokenSource.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error raising register value updated event");
        }
    }

    /// <summary>
    /// Raises the WriteOperationCompleted event
    /// </summary>
    /// <param name="eventArgs">Event arguments</param>
    private void OnWriteOperationCompleted(WriteOperationCompletedEventArgs eventArgs)
    {
        try
        {
            if (eventArgs.IsSuccessful)
            {
                _logger.LogDebug("Write operation {OperationId} completed successfully in {Duration}ms",
                    eventArgs.OperationId, eventArgs.Duration.TotalMilliseconds);
            }
            else
            {
                _logger.LogWarning(eventArgs.Error, "Write operation {OperationId} failed after {Duration}ms",
                    eventArgs.OperationId, eventArgs.Duration.TotalMilliseconds);
            }

            WriteOperationCompleted?.Invoke(this, eventArgs);

            // Notify event handlers
            NotifyEventHandlersAsync(handler => handler.HandleWriteOperationCompletedAsync(eventArgs, _cancellationTokenSource.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error raising write operation completed event");
        }
    }

    /// <summary>
    /// Notifies all event handlers asynchronously
    /// </summary>
    /// <param name="handlerAction">Action to execute on each handler</param>
    private void NotifyEventHandlersAsync(Func<IModbusEventHandler, Task> handlerAction)
    {
        if (_eventHandlers.Count == 0) return;

        // Execute event handlers in background to avoid blocking
        Task.Run(async () =>
        {
            IModbusEventHandler[] handlers;
            lock (_eventHandlersLock)
            {
                handlers = _eventHandlers.ToArray();
            }

            var tasks = handlers.Select(async handler =>
            {
                try
                {
                    await handlerAction(handler);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in event handler {HandlerType}", handler.GetType().Name);
                }
            });

            try
            {
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing event handlers");
            }
        }, _cancellationTokenSource.Token);
    }
}
