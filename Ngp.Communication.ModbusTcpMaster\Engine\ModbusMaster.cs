using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Connection;

namespace Ngp.Communication.ModbusTcpMaster.Engine;

/// <summary>
/// Main Modbus TCP Master engine implementation
/// </summary>
public class ModbusMaster : IModbusMaster
{
    private readonly ILogger<ModbusMaster> _logger;
    private readonly TcpConnectionPool _connectionPool;
    private readonly object _lockObject = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private ushort _nextTransactionId = 1;
    private bool _disposed = false;
    private bool _isStarted = false;
    
    /// <summary>
    /// Protocol mode (TCP or RTU over TCP)
    /// </summary>
    public ModbusProtocolMode ProtocolMode { get; private set; } = ModbusProtocolMode.ModbusTcp;
    
    /// <summary>
    /// Default timeout for requests
    /// </summary>
    public TimeSpan DefaultTimeout { get; private set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Event raised when data values are updated
    /// </summary>
    public event EventHandler<DataValueUpdatedEventArgs>? DataValueUpdated;
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
    
    /// <summary>
    /// Event raised when communication errors occur
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
    
    /// <summary>
    /// Event raised when Modbus exceptions occur
    /// </summary>
    public event EventHandler<ModbusExceptionEventArgs>? ModbusExceptionOccurred;
    
    /// <summary>
    /// Initializes a new instance of the ModbusMaster class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public ModbusMaster(ILogger<ModbusMaster>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<ModbusMaster>.Instance;
        _connectionPool = new TcpConnectionPool();
    }
    
    /// <summary>
    /// Start the Modbus master engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the start operation</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusMaster));
        
        if (_isStarted)
            return;
        
        _logger.LogInformation("Starting Modbus Master engine");
        
        _isStarted = true;
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Stop the Modbus master engine
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the stop operation</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || !_isStarted)
            return;
        
        _logger.LogInformation("Stopping Modbus Master engine");
        
        _cancellationTokenSource.Cancel();
        _isStarted = false;
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// Read coils from a Modbus device
    /// </summary>
    public async Task<bool[]> ReadCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} coils from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadCoils,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetCoilValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read discrete inputs from a Modbus device
    /// </summary>
    public async Task<bool[]> ReadDiscreteInputsAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} discrete inputs from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadDiscreteInputs,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetCoilValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read holding registers from a Modbus device
    /// </summary>
    public async Task<ushort[]> ReadHoldingRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} holding registers from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadHoldingRegisters,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetRegisterValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Read input registers from a Modbus device
    /// </summary>
    public async Task<ushort[]> ReadInputRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort quantity,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Reading {Quantity} input registers from {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            quantity, ipAddress, port, unitId, startAddress);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.ReadInputRegisters,
            StartingAddress = startAddress,
            Quantity = quantity,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = quantity,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            return response.GetRegisterValues(quantity);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = quantity
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Write a single coil to a Modbus device
    /// </summary>
    public async Task WriteSingleCoilAsync(string ipAddress, int port, byte unitId, ushort address, bool value,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Writing single coil to {IpAddress}:{Port} unit {UnitId} address {Address} value {Value}",
            ipAddress, port, unitId, address, value);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteSingleCoil,
            StartingAddress = address,
            Quantity = 1,
            Data = new byte[] { (byte)(value ? 1 : 0) },
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = address,
                    Quantity = 1,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            _logger.LogDebug("Successfully wrote single coil to {IpAddress}:{Port} unit {UnitId} address {Address}",
                ipAddress, port, unitId, address);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = address,
                Quantity = 1
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Write a single register to a Modbus device
    /// </summary>
    public async Task WriteSingleRegisterAsync(string ipAddress, int port, byte unitId, ushort address, ushort value,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Writing single register to {IpAddress}:{Port} unit {UnitId} address {Address} value {Value}",
            ipAddress, port, unitId, address, value);

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteSingleRegister,
            StartingAddress = address,
            Quantity = 1,
            Data = new byte[] { (byte)(value >> 8), (byte)(value & 0xFF) },
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = address,
                    Quantity = 1,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            _logger.LogDebug("Successfully wrote single register to {IpAddress}:{Port} unit {UnitId} address {Address}",
                ipAddress, port, unitId, address);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = address,
                Quantity = 1
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Write multiple coils to a Modbus device
    /// </summary>
    public async Task WriteMultipleCoilsAsync(string ipAddress, int port, byte unitId, ushort startAddress, bool[] values,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Writing {Count} coils to {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            values.Length, ipAddress, port, unitId, startAddress);

        // Convert boolean array to byte array
        var byteCount = (values.Length + 7) / 8; // Round up to nearest byte
        var data = new byte[byteCount];

        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                data[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteMultipleCoils,
            StartingAddress = startAddress,
            Quantity = (ushort)values.Length,
            Data = data,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = (ushort)values.Length,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            _logger.LogDebug("Successfully wrote {Count} coils to {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
                values.Length, ipAddress, port, unitId, startAddress);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = (ushort)values.Length
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Write multiple registers to a Modbus device
    /// </summary>
    public async Task WriteMultipleRegistersAsync(string ipAddress, int port, byte unitId, ushort startAddress, ushort[] values,
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Writing {Count} registers to {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
            values.Length, ipAddress, port, unitId, startAddress);

        // Convert ushort array to byte array (big-endian)
        var data = new byte[values.Length * 2];
        for (int i = 0; i < values.Length; i++)
        {
            data[i * 2] = (byte)(values[i] >> 8);     // High byte
            data[i * 2 + 1] = (byte)(values[i] & 0xFF); // Low byte
        }

        var request = new ModbusRequest
        {
            TransactionId = GetNextTransactionId(),
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteMultipleRegisters,
            StartingAddress = startAddress,
            Quantity = (ushort)values.Length,
            Data = data,
            Timeout = timeout ?? DefaultTimeout
        };

        try
        {
            var response = await _connectionPool.SendRequestAsync(ipAddress, port, request, ProtocolMode, cancellationToken);

            if (response.IsException)
            {
                var exceptionArgs = new ModbusExceptionEventArgs
                {
                    IpAddress = ipAddress,
                    Port = port,
                    UnitId = unitId,
                    FunctionCode = request.FunctionCode,
                    ExceptionCode = response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                    StartAddress = startAddress,
                    Quantity = (ushort)values.Length,
                    TransactionId = request.TransactionId
                };

                ModbusExceptionOccurred?.Invoke(this, exceptionArgs);
                throw new InvalidOperationException($"Modbus exception: {response.ExceptionCode}");
            }

            _logger.LogDebug("Successfully wrote {Count} registers to {IpAddress}:{Port} unit {UnitId} starting at address {StartAddress}",
                values.Length, ipAddress, port, unitId, startAddress);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = ipAddress,
                Port = port,
                UnitId = unitId,
                ErrorType = CommunicationErrorType.NetworkError,
                ErrorMessage = ex.Message,
                Exception = ex,
                FunctionCode = request.FunctionCode,
                StartAddress = startAddress,
                Quantity = (ushort)values.Length
            };

            CommunicationError?.Invoke(this, errorArgs);
            throw;
        }
    }
    
    /// <summary>
    /// Get next transaction ID
    /// </summary>
    /// <returns>Next transaction ID</returns>
    protected ushort GetNextTransactionId()
    {
        lock (_lockObject)
        {
            return _nextTransactionId++;
        }
    }
    
    /// <summary>
    /// Dispose the ModbusMaster
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _logger.LogInformation("Disposing Modbus Master");

        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();

        _connectionPool.Dispose();

        _disposed = true;
        GC.SuppressFinalize(this);
    }
}
