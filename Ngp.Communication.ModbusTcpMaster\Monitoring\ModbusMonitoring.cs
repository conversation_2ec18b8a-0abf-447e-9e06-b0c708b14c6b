using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Engine;

namespace Ngp.Communication.ModbusTcpMaster.Monitoring;

/// <summary>
/// Comprehensive monitoring system for Modbus operations
/// </summary>
public class ModbusMonitoring : IDisposable
{
    private readonly ILogger<ModbusMonitoring> _logger;
    private readonly ConcurrentDictionary<string, DeviceStatistics> _deviceStatistics;
    private readonly ConcurrentDictionary<ModbusFunctionCode, FunctionCodeStatistics> _functionCodeStatistics;
    private readonly Timer _statisticsTimer;
    private readonly object _lockObject = new();
    private bool _disposed = false;
    
    /// <summary>
    /// Overall system statistics
    /// </summary>
    public SystemStatistics SystemStatistics { get; private set; }
    
    /// <summary>
    /// Statistics collection interval
    /// </summary>
    public TimeSpan StatisticsInterval { get; set; } = TimeSpan.FromSeconds(10);
    
    /// <summary>
    /// Maximum number of devices to track
    /// </summary>
    public int MaxDevicesToTrack { get; set; } = 1000;
    
    /// <summary>
    /// Event raised when statistics are updated
    /// </summary>
    public event EventHandler<StatisticsUpdatedEventArgs>? StatisticsUpdated;
    
    /// <summary>
    /// Event raised when performance thresholds are exceeded
    /// </summary>
    public event EventHandler<PerformanceAlertEventArgs>? PerformanceAlert;
    
    /// <summary>
    /// Initializes a new instance of the ModbusMonitoring class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public ModbusMonitoring(ILogger<ModbusMonitoring>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<ModbusMonitoring>.Instance;
        _deviceStatistics = new ConcurrentDictionary<string, DeviceStatistics>();
        _functionCodeStatistics = new ConcurrentDictionary<ModbusFunctionCode, FunctionCodeStatistics>();
        
        SystemStatistics = new SystemStatistics
        {
            StartTime = DateTime.UtcNow
        };
        
        // Initialize function code statistics
        foreach (ModbusFunctionCode functionCode in Enum.GetValues<ModbusFunctionCode>())
        {
            _functionCodeStatistics.TryAdd(functionCode, new FunctionCodeStatistics
            {
                FunctionCode = functionCode
            });
        }
        
        // Start statistics collection timer
        _statisticsTimer = new Timer(CollectStatistics, null, StatisticsInterval, StatisticsInterval);
        
        _logger.LogInformation("Modbus monitoring started");
    }
    
    /// <summary>
    /// Record a successful request
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit ID</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="responseTime">Response time</param>
    /// <param name="dataSize">Data size in bytes</param>
    public void RecordSuccessfulRequest(string ipAddress, int port, byte unitId, ModbusFunctionCode functionCode, 
        TimeSpan responseTime, int dataSize = 0)
    {
        var deviceKey = $"{ipAddress}:{port}:{unitId}";
        
        // Update device statistics
        var deviceStats = _deviceStatistics.GetOrAdd(deviceKey, _ => new DeviceStatistics
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FirstSeen = DateTime.UtcNow
        });
        
        lock (deviceStats.LockObject)
        {
            deviceStats.TotalRequests++;
            deviceStats.SuccessfulRequests++;
            deviceStats.LastSeen = DateTime.UtcNow;
            deviceStats.TotalResponseTime += responseTime;
            deviceStats.TotalDataTransferred += dataSize;
            
            // Update min/max response times
            if (deviceStats.MinResponseTime == TimeSpan.Zero || responseTime < deviceStats.MinResponseTime)
                deviceStats.MinResponseTime = responseTime;
            
            if (responseTime > deviceStats.MaxResponseTime)
                deviceStats.MaxResponseTime = responseTime;
        }
        
        // Update function code statistics
        if (_functionCodeStatistics.TryGetValue(functionCode, out var funcStats))
        {
            lock (funcStats.LockObject)
            {
                funcStats.TotalRequests++;
                funcStats.SuccessfulRequests++;
                funcStats.TotalResponseTime += responseTime;
                
                if (funcStats.MinResponseTime == TimeSpan.Zero || responseTime < funcStats.MinResponseTime)
                    funcStats.MinResponseTime = responseTime;
                
                if (responseTime > funcStats.MaxResponseTime)
                    funcStats.MaxResponseTime = responseTime;
            }
        }
        
        // Update system statistics
        lock (_lockObject)
        {
            SystemStatistics.TotalRequests++;
            SystemStatistics.SuccessfulRequests++;
            SystemStatistics.TotalResponseTime += responseTime;
            SystemStatistics.TotalDataTransferred += dataSize;
        }
        
        // Check for performance alerts
        CheckPerformanceThresholds(deviceKey, responseTime);
    }
    
    /// <summary>
    /// Record a failed request
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit ID</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="errorType">Error type</param>
    /// <param name="errorMessage">Error message</param>
    public void RecordFailedRequest(string ipAddress, int port, byte unitId, ModbusFunctionCode functionCode, 
        CommunicationErrorType errorType, string errorMessage)
    {
        var deviceKey = $"{ipAddress}:{port}:{unitId}";
        
        // Update device statistics
        var deviceStats = _deviceStatistics.GetOrAdd(deviceKey, _ => new DeviceStatistics
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FirstSeen = DateTime.UtcNow
        });
        
        lock (deviceStats.LockObject)
        {
            deviceStats.TotalRequests++;
            deviceStats.FailedRequests++;
            deviceStats.LastSeen = DateTime.UtcNow;
            deviceStats.LastError = errorMessage;
            deviceStats.LastErrorTime = DateTime.UtcNow;
            
            // Update error type statistics
            if (!deviceStats.ErrorTypeCount.ContainsKey(errorType))
                deviceStats.ErrorTypeCount[errorType] = 0;
            deviceStats.ErrorTypeCount[errorType]++;
        }
        
        // Update function code statistics
        if (_functionCodeStatistics.TryGetValue(functionCode, out var funcStats))
        {
            lock (funcStats.LockObject)
            {
                funcStats.TotalRequests++;
                funcStats.FailedRequests++;
            }
        }
        
        // Update system statistics
        lock (_lockObject)
        {
            SystemStatistics.TotalRequests++;
            SystemStatistics.FailedRequests++;
            
            if (!SystemStatistics.ErrorTypeCount.ContainsKey(errorType))
                SystemStatistics.ErrorTypeCount[errorType] = 0;
            SystemStatistics.ErrorTypeCount[errorType]++;
        }
        
        _logger.LogWarning("Request failed: {DeviceKey} {FunctionCode} {ErrorType} {ErrorMessage}",
            deviceKey, functionCode, errorType, errorMessage);
    }
    
    /// <summary>
    /// Record a Modbus exception
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit ID</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="exceptionCode">Exception code</param>
    public void RecordModbusException(string ipAddress, int port, byte unitId, ModbusFunctionCode functionCode, 
        ModbusExceptionCode exceptionCode)
    {
        var deviceKey = $"{ipAddress}:{port}:{unitId}";
        
        // Update device statistics
        var deviceStats = _deviceStatistics.GetOrAdd(deviceKey, _ => new DeviceStatistics
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FirstSeen = DateTime.UtcNow
        });
        
        lock (deviceStats.LockObject)
        {
            deviceStats.TotalRequests++;
            deviceStats.ModbusExceptions++;
            deviceStats.LastSeen = DateTime.UtcNow;
            deviceStats.LastError = $"Modbus Exception: {exceptionCode}";
            deviceStats.LastErrorTime = DateTime.UtcNow;
            
            // Update exception code statistics
            if (!deviceStats.ExceptionCodeCount.ContainsKey(exceptionCode))
                deviceStats.ExceptionCodeCount[exceptionCode] = 0;
            deviceStats.ExceptionCodeCount[exceptionCode]++;
        }
        
        // Update function code statistics
        if (_functionCodeStatistics.TryGetValue(functionCode, out var funcStats))
        {
            lock (funcStats.LockObject)
            {
                funcStats.TotalRequests++;
                funcStats.ModbusExceptions++;
            }
        }
        
        // Update system statistics
        lock (_lockObject)
        {
            SystemStatistics.TotalRequests++;
            SystemStatistics.ModbusExceptions++;
            
            if (!SystemStatistics.ExceptionCodeCount.ContainsKey(exceptionCode))
                SystemStatistics.ExceptionCodeCount[exceptionCode] = 0;
            SystemStatistics.ExceptionCodeCount[exceptionCode]++;
        }
        
        _logger.LogWarning("Modbus exception: {DeviceKey} {FunctionCode} {ExceptionCode}",
            deviceKey, functionCode, exceptionCode);
    }
    
    /// <summary>
    /// Get statistics for a specific device
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit ID</param>
    /// <returns>Device statistics or null if not found</returns>
    public DeviceStatistics? GetDeviceStatistics(string ipAddress, int port, byte unitId)
    {
        var deviceKey = $"{ipAddress}:{port}:{unitId}";
        return _deviceStatistics.TryGetValue(deviceKey, out var stats) ? stats : null;
    }
    
    /// <summary>
    /// Get statistics for all devices
    /// </summary>
    /// <returns>Collection of device statistics</returns>
    public IEnumerable<DeviceStatistics> GetAllDeviceStatistics()
    {
        return _deviceStatistics.Values.ToList();
    }
    
    /// <summary>
    /// Get statistics for a specific function code
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <returns>Function code statistics</returns>
    public FunctionCodeStatistics? GetFunctionCodeStatistics(ModbusFunctionCode functionCode)
    {
        return _functionCodeStatistics.TryGetValue(functionCode, out var stats) ? stats : null;
    }
    
    /// <summary>
    /// Get statistics for all function codes
    /// </summary>
    /// <returns>Collection of function code statistics</returns>
    public IEnumerable<FunctionCodeStatistics> GetAllFunctionCodeStatistics()
    {
        return _functionCodeStatistics.Values.ToList();
    }
    
    /// <summary>
    /// Get comprehensive monitoring report
    /// </summary>
    /// <returns>Monitoring report</returns>
    public MonitoringReport GetMonitoringReport()
    {
        var uptime = DateTime.UtcNow - SystemStatistics.StartTime;
        
        return new MonitoringReport
        {
            SystemStatistics = SystemStatistics,
            DeviceStatistics = GetAllDeviceStatistics().ToList(),
            FunctionCodeStatistics = GetAllFunctionCodeStatistics().ToList(),
            Uptime = uptime,
            GeneratedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// Reset all statistics
    /// </summary>
    public void ResetStatistics()
    {
        lock (_lockObject)
        {
            _deviceStatistics.Clear();
            
            foreach (var funcStats in _functionCodeStatistics.Values)
            {
                lock (funcStats.LockObject)
                {
                    funcStats.Reset();
                }
            }
            
            SystemStatistics = new SystemStatistics
            {
                StartTime = DateTime.UtcNow
            };
        }
        
        _logger.LogInformation("Statistics reset");
    }
    
    /// <summary>
    /// Check performance thresholds and raise alerts
    /// </summary>
    /// <param name="deviceKey">Device key</param>
    /// <param name="responseTime">Response time</param>
    private void CheckPerformanceThresholds(string deviceKey, TimeSpan responseTime)
    {
        // Check for slow response time (configurable threshold)
        var slowResponseThreshold = TimeSpan.FromSeconds(2);
        if (responseTime > slowResponseThreshold)
        {
            var alertArgs = new PerformanceAlertEventArgs
            {
                AlertType = PerformanceAlertType.SlowResponse,
                DeviceKey = deviceKey,
                Threshold = slowResponseThreshold,
                ActualValue = responseTime,
                Message = $"Slow response time detected: {responseTime.TotalMilliseconds:F2}ms"
            };
            
            PerformanceAlert?.Invoke(this, alertArgs);
        }
    }
    
    /// <summary>
    /// Collect and update statistics periodically
    /// </summary>
    /// <param name="state">Timer state</param>
    private void CollectStatistics(object? state)
    {
        try
        {
            // Update calculated statistics
            lock (_lockObject)
            {
                var uptime = DateTime.UtcNow - SystemStatistics.StartTime;
                SystemStatistics.Uptime = uptime;
                
                if (SystemStatistics.TotalRequests > 0)
                {
                    SystemStatistics.SuccessRate = (double)SystemStatistics.SuccessfulRequests / SystemStatistics.TotalRequests * 100;
                    SystemStatistics.AverageResponseTime = new TimeSpan(SystemStatistics.TotalResponseTime.Ticks / SystemStatistics.SuccessfulRequests);
                }
                
                SystemStatistics.RequestsPerSecond = uptime.TotalSeconds > 0 ? SystemStatistics.TotalRequests / uptime.TotalSeconds : 0;
            }
            
            // Update device statistics
            foreach (var deviceStats in _deviceStatistics.Values)
            {
                lock (deviceStats.LockObject)
                {
                    if (deviceStats.TotalRequests > 0)
                    {
                        deviceStats.SuccessRate = (double)deviceStats.SuccessfulRequests / deviceStats.TotalRequests * 100;
                        
                        if (deviceStats.SuccessfulRequests > 0)
                        {
                            deviceStats.AverageResponseTime = new TimeSpan(deviceStats.TotalResponseTime.Ticks / deviceStats.SuccessfulRequests);
                        }
                    }
                }
            }
            
            // Update function code statistics
            foreach (var funcStats in _functionCodeStatistics.Values)
            {
                lock (funcStats.LockObject)
                {
                    if (funcStats.TotalRequests > 0)
                    {
                        funcStats.SuccessRate = (double)funcStats.SuccessfulRequests / funcStats.TotalRequests * 100;
                        
                        if (funcStats.SuccessfulRequests > 0)
                        {
                            funcStats.AverageResponseTime = new TimeSpan(funcStats.TotalResponseTime.Ticks / funcStats.SuccessfulRequests);
                        }
                    }
                }
            }
            
            // Raise statistics updated event
            var eventArgs = new StatisticsUpdatedEventArgs
            {
                SystemStatistics = SystemStatistics,
                ActiveDeviceCount = _deviceStatistics.Count,
                UpdatedAt = DateTime.UtcNow
            };
            
            StatisticsUpdated?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting statistics");
        }
    }
    
    /// <summary>
    /// Dispose the monitoring system
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _statisticsTimer.Dispose();

        _disposed = true;
        GC.SuppressFinalize(this);

        _logger.LogInformation("Modbus monitoring disposed");
    }
}

/// <summary>
/// Device statistics
/// </summary>
public class DeviceStatistics
{
    public string IpAddress { get; set; } = string.Empty;
    public int Port { get; set; }
    public byte UnitId { get; set; }
    public DateTime FirstSeen { get; set; }
    public DateTime LastSeen { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public long ModbusExceptions { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan TotalResponseTime { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MinResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public long TotalDataTransferred { get; set; }
    public string LastError { get; set; } = string.Empty;
    public DateTime LastErrorTime { get; set; }
    public Dictionary<CommunicationErrorType, int> ErrorTypeCount { get; set; } = new();
    public Dictionary<ModbusExceptionCode, int> ExceptionCodeCount { get; set; } = new();
    public object LockObject { get; } = new();
}

/// <summary>
/// Function code statistics
/// </summary>
public class FunctionCodeStatistics
{
    public ModbusFunctionCode FunctionCode { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public long ModbusExceptions { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan TotalResponseTime { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MinResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public object LockObject { get; } = new();

    public void Reset()
    {
        TotalRequests = 0;
        SuccessfulRequests = 0;
        FailedRequests = 0;
        ModbusExceptions = 0;
        SuccessRate = 0;
        TotalResponseTime = TimeSpan.Zero;
        AverageResponseTime = TimeSpan.Zero;
        MinResponseTime = TimeSpan.Zero;
        MaxResponseTime = TimeSpan.Zero;
    }
}

/// <summary>
/// System statistics
/// </summary>
public class SystemStatistics
{
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public long ModbusExceptions { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan TotalResponseTime { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public long TotalDataTransferred { get; set; }
    public double RequestsPerSecond { get; set; }
    public Dictionary<CommunicationErrorType, int> ErrorTypeCount { get; set; } = new();
    public Dictionary<ModbusExceptionCode, int> ExceptionCodeCount { get; set; } = new();
}

/// <summary>
/// Monitoring report
/// </summary>
public class MonitoringReport
{
    public SystemStatistics SystemStatistics { get; set; } = new();
    public List<DeviceStatistics> DeviceStatistics { get; set; } = new();
    public List<FunctionCodeStatistics> FunctionCodeStatistics { get; set; } = new();
    public TimeSpan Uptime { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Statistics updated event arguments
/// </summary>
public class StatisticsUpdatedEventArgs : EventArgs
{
    public SystemStatistics SystemStatistics { get; set; } = new();
    public int ActiveDeviceCount { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Performance alert event arguments
/// </summary>
public class PerformanceAlertEventArgs : EventArgs
{
    public PerformanceAlertType AlertType { get; set; }
    public string DeviceKey { get; set; } = string.Empty;
    public TimeSpan Threshold { get; set; }
    public TimeSpan ActualValue { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Performance alert types
/// </summary>
public enum PerformanceAlertType
{
    SlowResponse,
    HighErrorRate,
    ConnectionTimeout,
    MemoryUsage,
    CpuUsage
}
